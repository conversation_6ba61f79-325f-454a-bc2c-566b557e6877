📄 context.md — Sistem Pengelolaan Depot Air Minhaqua

🏢 Tentang Perusahaan



Minhaqua adalah perusahaan penyedia air isi ulang yang melayani pemesanan, pengantaran, dan manajemen stok air galon. Sistem ini dibuat untuk membantu pengelolaan depot secara digital serta mempermudah pemetaan rute pengiriman.



🎯 Tujuan Sistem



Mempermudah pencatatan pelanggan, pesanan, stok, dan kurir.



Menyediakan pemetaan interaktif (map) dengan pembagian wilayah (blok).



Membantu admin menentukan rute pengiriman air.



Menyediakan laporan penjualan \& riwayat pesanan.



📌 Fitur Utama

1\. Manajemen Data



Pelanggan → data nama, alamat, no HP, lokasi (latitude \& longitude).



Pesanan → jumlah galon, tanggal, status (menunggu, proses, dikirim, selesai).



Kurir → data kurir, no HP, username \& password untuk login, status aktif/tidak, serta riwayat pengantaran.



Stok Galon → galon isi \& kosong.



2\. Pemetaan



Menampilkan lokasi pelanggan di map.



Menampilkan wilayah blok custom (misalnya Blok A, Blok B, dsb) dengan koordinat polygon.



Menampilkan marker pelanggan di dalam blok.



Popup informasi saat klik blok atau marker.



Marker kurir dapat ditampilkan (opsional real-time tracking).



3\. Pengantaran



Admin assign pesanan ke kurir.



Kurir login dan melihat daftar pesanan yang ditugaskan.



Kurir bisa update status pesanan:



menunggu → dikirim → selesai.



Kurir dapat melihat detail pesanan beserta map lokasi pelanggan.



🎨 Palet Warna Biru Air Soft



Primary (Biru Air): #4DB6E2



Primary Dark (Biru Laut): #2581B5



Primary Light (Biru Soft Transparan): #E6F7FB



Accent (Hijau Tosca lembut, opsional): #3DDAD7



Text: #333333 (gelap biar kontras)



Background: #F9FAFB (hampir putih, lembut)

4\. Laporan



Total penjualan per hari/bulan.



Riwayat pesanan per pelanggan.



Riwayat pengiriman per kurir (jumlah order selesai, total galon diantar).



🛠️ Teknologi yang Digunakan



Backend: PHP Native (tanpa framework).



Database: MySQL.



Frontend: HTML, CSS, JavaScript.



Peta: LeafletJS (OpenStreetMap).



🗄️ Struktur Database (Draft)

-- Pelanggan

CREATE TABLE pelanggan (

  id INT AUTO\_INCREMENT PRIMARY KEY,

  nama VARCHAR(100),

  alamat TEXT,

  no\_hp VARCHAR(20),

  latitude DECIMAL(10,8),

  longitude DECIMAL(11,8)

);



-- Pesanan

CREATE TABLE pesanan (

  id INT AUTO\_INCREMENT PRIMARY KEY,

  pelanggan\_id INT,

  jumlah INT,

  tanggal TIMESTAMP DEFAULT CURRENT\_TIMESTAMP,

  status ENUM('menunggu','proses','dikirim','selesai') DEFAULT 'menunggu',

  FOREIGN KEY (pelanggan\_id) REFERENCES pelanggan(id)

);



-- Kurir

CREATE TABLE kurir (

  id INT AUTO\_INCREMENT PRIMARY KEY,

  nama VARCHAR(100),

  no\_hp VARCHAR(20),

  username VARCHAR(50),

  password VARCHAR(255),

  status ENUM('aktif','nonaktif') DEFAULT 'aktif'

);



-- Pengiriman

CREATE TABLE pengiriman (

  id INT AUTO\_INCREMENT PRIMARY KEY,

  pesanan\_id INT,

  kurir\_id INT,

  waktu\_berangkat TIMESTAMP NULL,

  waktu\_selesai TIMESTAMP NULL,

  FOREIGN KEY (pesanan\_id) REFERENCES pesanan(id),

  FOREIGN KEY (kurir\_id) REFERENCES kurir(id)

);



-- Blok/Wilayah

CREATE TABLE blok (

  id INT AUTO\_INCREMENT PRIMARY KEY,

  nama VARCHAR(50),

  koordinat TEXT -- disimpan dalam format JSON array (koordinat polygon)

);



🗺️ Contoh Data Blok (JSON)

\[

  {"lat": -6.200, "lng": 106.800},

  {"lat": -6.202, "lng": 106.805},

  {"lat": -6.205, "lng": 106.803},

  {"lat": -6.203, "lng": 106.798}

]



📂 Struktur Folder Project

Minhaqua/

│── index.php           # Dashboard Admin

│── config.php          # Koneksi database

│── /pages

│    ├── pelanggan.php

│    ├── pesanan.php

│    ├── kurir.php

│    ├── pengiriman.php

│    ├── blok.php

│── /kurir

│    ├── login.php      # Login khusus kurir

│    ├── dashboard.php  # Pesanan yang ditugaskan

│    ├── update.php     # Update status pesanan

│── /assets

│    ├── css/

│    ├── js/

│    ├── img/



🚀 Next Step



Buat database \& tabel sesuai struktur di atas.



Implementasi CRUD untuk pelanggan, pesanan, kurir, blok.



Tambahkan modul assign kurir ke pesanan di admin panel.



Buat halaman login \& dashboard untuk kurir agar bisa update status pesanan.



Integrasi LeafletJS untuk menampilkan blok + marker pelanggan + marker kurir.



Tambahkan modul laporan \& filter data.

